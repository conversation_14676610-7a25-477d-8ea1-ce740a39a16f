import 'dart:async';
import 'dart:developer';
import 'package:boutigak/controllers/connectivity_controlleur.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/notifications_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/translations.dart';
import 'package:boutigak/utils/deepLinkHandler.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:boutigak/data/middelware/middelware.dart';
import 'package:boutigak/views/onboarding/onboarding_page.dart';
import 'package:boutigak/views/profil/%20language_selector_page.dart';
import 'views/registerandlogin/register_page.dart';
import 'bindings/register_bindings.dart';
import 'views/registerandlogin/login_page.dart';
import 'bindings/login_bindings.dart';
import 'bindings/nav_bindings.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:boutigak/controllers/nav_controller.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:google_fonts/google_fonts.dart';



final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  Get.find<BadgeController>().fetchBadgeCounts();
  showLocalNotification(message);
}
 

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  Get.put(AuthController());
  Get.put(BadgeController());
  Get.put(OrderController());
  Get.put(ConnectivityController());
  Get.put(ItemController());
  Get.put(NavController());
  Get.lazyPut(()=>InboxController());
  Get.put(NotificationController());

  Locale locale = await _getSavedLocale();
  Get.updateLocale(locale);

  bool isBadgeSupported = await FlutterAppBadger.isAppBadgeSupported();
  print('Badge supporté : $isBadgeSupported');
  resetBadgeOnAppOpen();

  FirebaseMessaging messaging = FirebaseMessaging.instance;
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  await messaging.requestPermission(alert: true, badge: true, sound: true);

  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  final DarwinInitializationSettings initializationSettingsDarwin =
      DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
  );


  final InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsDarwin,
  );



  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      if (response.payload != null) {
        showDialog(
          context: navigatorKey.currentState!.overlay!.context,
          builder: (_) => CupertinoAlertDialog(
            title: const Text("Notification"),
            content: Text(response.payload!),
            actions: [
              CupertinoDialogAction(
                child: const Text("OK"),
                onPressed: () => Navigator.of(navigatorKey.currentState!.context).pop(),
              ),
            ],
          ),
        );
      }
    },
  );

  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'your_channel_id',
    'your_channel_name',
    description: 'your_channel_description',
    importance: Importance.max,
    playSound: true,
    enableVibration: true,
    sound: null, 
  );

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  try {
    String? token = await messaging.getToken();
    print('FCM Token: $token');
  } catch (e) {
    print('Erreur token FCM: $e');
  }

  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    showLocalNotification(message);
    Get.find<BadgeController>().fetchBadgeCounts();

    if (message.data['type'] == 'message' && message.data['discussion_id'] != null) {
      try {
        final discussionId = int.parse(message.data['discussion_id']);
        if (Get.isRegistered<ConversationController>()) {
          Get.find<ConversationController>().refreshMessagesIfInConversation(discussionId);
        }
      } catch (e) {
        print('Error handling message notification: $e');
      }
    }
  });

  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
    print('Notification ouverte: ${message.notification?.title}');
    await resetBadgeOnAppOpen();
  });

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  runApp(MyApp(locale));
}

// 🌍 MyApp Widget
class MyApp extends StatefulWidget {
  final Locale initialLocale;

  const MyApp(this.initialLocale, {super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    DeepLinkHandler.initDeepLinks();
  }

  @override
  void dispose() {
    DeepLinkHandler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ThemeNotifier>(
          create: (_) => ThemeNotifier(lightTheme, false),
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(393, 852),
        builder: (context, child) {
          final themeNotifier = Provider.of<ThemeNotifier>(context);

          return GetMaterialApp(
            navigatorKey: navigatorKey,
            title: 'Boutigak',
            debugShowCheckedModeBanner: false,
            theme: themeNotifier.themeData,
            translations: Language(),
            locale: widget.initialLocale,
            fallbackLocale: const Locale('en', 'US'),
            home: SplashScreen(),
            getPages: [
              GetPage(
                name: '/register',
                page: () => const RegisterPage(),
                binding: RegisterBindings(),
              ),
              GetPage(
                name: '/login',
                page: () => const LoginPage(),
                binding: LoginBinding(),
              ),
            
              GetPage(
                name: '/',
                page: () => const NavigationBarPage(),
                binding: NavBindings(),
              ),
              GetPage(
                name: '/onboarding',
                page: () => const OnboardingScreen(),
              ),
            ],
          );
        },
      ),
    );
  }
}

Future<Locale> _getSavedLocale() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? languageCode = prefs.getString('language_code');
  String? countryCode = prefs.getString('country_code');

  if (languageCode == null || countryCode == null) {
    Locale deviceLocale = PlatformDispatcher.instance.locale;
    languageCode = deviceLocale.languageCode;
    countryCode = deviceLocale.countryCode ?? 'GB';

    await prefs.setString('language_code', languageCode);
    await prefs.setString('country_code', countryCode);
  }

  return Locale(languageCode, countryCode);
}

void showLocalNotification(RemoteMessage message) async {

  log('🔔 Notification reçue: ${message.notification?.title}');

  const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    'your_channel_id',
    'your_channel_name',
    channelDescription: 'your_channel_description',
    importance: Importance.max,
    priority: Priority.high,
    showWhen: true,
    playSound: true,
    sound: null,
    enableVibration: true,
  );

  const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
    sound: 'default',
  );

  final NotificationDetails platformDetails = NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );

  await flutterLocalNotificationsPlugin.show(
    0,
    message.notification?.title ?? 'No Title',
    message.notification?.body ?? 'No Body',
    platformDetails,
    payload: message.notification?.body ?? 'Notification',
  );

  int currentCount = await getUnreadNotificationCount();
  int newCount = currentCount + 1;
  await updateUnreadNotificationCount(newCount);
}

Future<int> getUnreadNotificationCount() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.getInt('unread_notifications') ?? 0;
}

Future<void> updateUnreadNotificationCount(int count) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setInt('unread_notifications', count);
  print('🔔 Badge MAJ: $count');
  FlutterAppBadger.updateBadgeCount(count);
}

Future<void> resetBadgeOnAppOpen() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setInt('unread_notifications', 0);
  print('🔄 Badge réinitialisé');
  FlutterAppBadger.removeBadge();
}



// void openWhatsApp(String phoneNumber) async {
//   final Uri url = Uri.parse('https://wa.me/$phoneNumber');

//   if (await canLaunchUrl(url)) {
//     await launchUrl(url);
//   } else {
//     throw 'Could not launch $url';
//   }
// }


  //  Padding(
  //           padding: EdgeInsets.symmetric(
  //             horizontal: 16.w,
  //                                        ),
  //           child: SizedBox(
  //             width: 80.w,
  //             height: 30.h,
  //             child: ElevatedButton(
  //              onPressed: () {
                
  //   String phoneNumber = '22236666688'; 
  //   openWhatsApp(phoneNumber);
  //               },
  //               style: ElevatedButton.styleFrom(
  //                 foregroundColor: AppColors.primary,
  //                 backgroundColor: Theme.of(context).colorScheme.surface,
  //                 shape: RoundedRectangleBorder(
  //                   borderRadius: BorderRadius.circular(10.r),
  //                 ),
  //                 textStyle:  TextStyle(
  //                   fontSize: AppTextSizes.bodySmall.sp,
  //                   fontWeight: AppFontWeights.bold,
  //                 ),
  //               ),
  //               child: const Text("Help"),
  //             ),
  //           ),
  //         ),