import 'dart:convert';
import 'dart:developer';
import 'package:boutigak/data/services/webservices.dart';

class BlockingService {
  
  /// Block a user
  static Future<Map<String, dynamic>?> blockUser(int userId) async {
    try {

      log('blocking user $userId');
      
      final response = await WebService.post(
        AvailableServices.blockUser,
        body: {
          'blocked_user_id': userId,
        },
      );

      log('Block user response: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        log('Failed to block user. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      log("Error blocking user: $e");
      return null;
    }
  }

  /// Unblock a user
  static Future<Map<String, dynamic>?> unblockUser(int userId) async {
    try {

      
      final response = await WebService.post(
        AvailableServices.unblockUser,
        body: {
          'blocked_user_id': userId,
        },
      );

      log('Unblock user response: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        log('Failed to unblock user. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      log("Error unblocking user: $e");
      return null;
    }
  }

  /// Check if a user is blocked
  static Future<Map<String, dynamic>?> checkBlockStatus(int userId) async {
    try {
      final response = await WebService.post(
        AvailableServices.checkBlockStatus,
        body: {
          'user_id': userId,
        },
      );

      log('Check block status response: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        log('Failed to check block status. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      log("Error checking block status: $e");
      return null;
    }
  }

  /// Get list of blocked users
  static Future<Map<String, dynamic>?> getBlockedUsers() async {
    try {
      final response = await WebService.get(
        AvailableServices.getBlockedUsers,
      );

      log('Get blocked users response: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        log('Failed to get blocked users. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      log("Error getting blocked users: $e");
      return null;
    }
  }
}
