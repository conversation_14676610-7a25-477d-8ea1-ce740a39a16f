<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserBlock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class UserBlockController extends Controller
{
    /**
     * Block a user
     */
    public function blockUser(Request $request)
    {
        try {
            $request->validate([
                'blocked_user_id' => 'required|integer|exists:users,id'
            ]);

            $blockerId = Auth::id();
            $blockedUserId = $request->blocked_user_id;

            Log::info('blockerId ' . $blockerId);
            Log::info('blockedUserId ' . $blockedUserId);


            if ($blockerId == $blockedUserId) {
                return response()->json([
                    'message' => 'You cannot block yourself'
                ], 400);
            }

            // Check if already blocked
            $existingBlock = UserBlock::where('blocker_id', $blockerId)
                ->where('blocked_id', $blockedUserId)
                ->first();

            if ($existingBlock) {
                return response()->json([
                    'message' => 'User is already blocked'
                ], 400);
            }

            // Create the block
            UserBlock::create([
                'blocker_id' => $blockerId,
                'blocked_id' => $blockedUserId
            ]);

            return response()->json([
                'message' => 'User blocked successfully'
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error blocking user: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to block user'
            ], 500);
        }
    }

    /**
     * Unblock a user
     */
    public function unblockUser(Request $request)
    {
        try {
            $request->validate([
                'blocked_user_id' => 'required|integer|exists:users,id'
            ]);

            $blockerId = Auth::id();
            $blockedUserId = $request->blocked_user_id;

            // Find and delete the block
            $block = UserBlock::where('blocker_id', $blockerId)
                ->where('blocked_id', $blockedUserId)
                ->first();

            if (!$block) {
                return response()->json([
                    'message' => 'User is not blocked'
                ], 400);
            }

            $block->delete();

            return response()->json([
                'message' => 'User unblocked successfully'
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error unblocking user: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to unblock user'
            ], 500);
        }
    }

    /**
     * Check if a user is blocked
     */
    public function checkBlockStatus(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|integer|exists:users,id'
            ]);

            $currentUserId = Auth::id();
            $targetUserId = $request->user_id;

            // Check if current user has blocked the target user
            $isBlocked = UserBlock::where('blocker_id', $currentUserId)
                ->where('blocked_id', $targetUserId)
                ->exists();

            // Check if target user has blocked the current user
            $isBlockedBy = UserBlock::where('blocker_id', $targetUserId)
                ->where('blocked_id', $currentUserId)
                ->exists();

            return response()->json([
                'is_blocked' => $isBlocked,
                'is_blocked_by' => $isBlockedBy
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error checking block status: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to check block status'
            ], 500);
        }
    }

    /**
     * Get list of blocked users
     */
    public function getBlockedUsers()
    {
        try {
            $userId = Auth::id();

            $blockedUsers = UserBlock::where('blocker_id', $userId)
                ->with(['blockedUser:id,firstname,lastname,phone'])
                ->get()
                ->map(function ($block) {
                    return [
                        'id' => $block->blockedUser->id,
                        'firstname' => $block->blockedUser->firstname,
                        'lastname' => $block->blockedUser->lastname,
                        'phone' => $block->blockedUser->phone,
                        'blocked_at' => $block->created_at
                    ];
                });

            return response()->json([
                'blocked_users' => $blockedUsers
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error getting blocked users: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get blocked users'
            ], 500);
        }
    }
}
