<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserBlock extends Model
{
    use HasFactory;

    protected $table = 'user_blocks';

    protected $fillable = [
        'blocker_id',
        'blocked_id'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    /**
     * Get the user who is doing the blocking
     */
    public function blocker()
    {
        return $this->belongsTo(User::class, 'blocker_id');
    }

    /**
     * Get the user who is being blocked
     */
    public function blockedUser()
    {
        return $this->belongsTo(User::class, 'blocked_id');
    }

    /**
     * Check if a user is blocked by another user
     */
    public static function isBlocked($blockerId, $blockedUserId)
    {
        return self::where('blocker_id', $blockerId)
            ->where('blocked_id', $blockedUserId)
            ->exists();
    }

    /**
     * Check if two users have blocked each other (either direction)
     */
    public static function areUsersBlocked($userId1, $userId2)
    {
        return self::where(function ($query) use ($userId1, $userId2) {
            $query->where('blocker_id', $userId1)
                  ->where('blocked_id', $userId2);
        })->orWhere(function ($query) use ($userId1, $userId2) {
            $query->where('blocker_id', $userId2)
                  ->where('blocked_id', $userId1);
        })->exists();
    }
}
